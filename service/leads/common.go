package leads

import (
	"assistantdeskgo/api/mercury"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type BlockedFeatureConfig struct {
	Name []string `json:"name"`
}

func GetBlockedFeatureConfig(ctx *gin.Context) (res BlockedFeatureConfig) {
	res = BlockedFeatureConfig{}
	if err := mercury.GetConfigForJson(ctx, "ark_blocked_feature_config", mercury.DefaultExpireTime, &res); err != nil {
		zlog.Warnf(ctx, "GetBlockedFeatureConfig get mercury config failed, err: %+v", err)
	}
	return
}

// FeatureFormatType 定义字段格式化类型常量
const (
	FeatureTypeRaw        = "raw"        // 原始值，不做任何处理
	FeatureTypeEnum       = "enum"       // 枚举值翻译
	FeatureTypeTimestamp  = "timestamp"  // 时间戳格式化
	FeatureTypePercentage = "percentage" // 百分比格式化
	FeatureTypeCurrency   = "currency"   // 货币格式化
)

// LeadsFeatureConfig 特征字段配置，支持多种格式化类型
type LeadsFeatureConfig struct {
	Name           string            `json:"name"`            // 字段名
	Desc           string            `json:"desc"`            // 字段描述
	Type           string            `json:"type"`            // 格式化类型：raw, enum, timestamp, percentage, currency
	Mapping        map[string]string `json:"mapping"`         // 枚举值映射（type=enum时使用）
	TimeFormat     string            `json:"time_format"`     // 时间格式（type=timestamp时使用，默认：2006-01-02 15:04:05）
	DecimalPlace   int               `json:"decimal_place"`   // 小数位数（type=percentage/currency时使用，默认：1）
	DivisionFactor int               `json:"division_factor"` // 除数因子（type=currency时使用，如100表示从分转元，默认：1）
}

func GetLeadsFeatureConfig(ctx *gin.Context) (res []LeadsFeatureConfig, err error) {
	res = []LeadsFeatureConfig{}
	if err := mercury.GetConfigForJson(ctx, "ark_leads_feature_config", mercury.DefaultExpireTime, &res); err != nil {
		zlog.Warnf(ctx, "GetLeadsFeatureConfig get mercury config failed, err: %+v", err)
	}
	return res, nil
}

// isEmptyValue 检测值是否为空值
// 空值包括：nil、""、"-"、"-999"
func isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}

	switch v := value.(type) {
	case string:
		return v == "" || v == "-" || v == "-999" || v == "null"
	}
	return false
}

// FormatFeatureValue 根据配置格式化字段值
func FormatFeatureValue(ctx *gin.Context, config LeadsFeatureConfig, value interface{}) string {
	// 统一的空值处理：所有空值转换为"暂无"
	if isEmptyValue(value) {
		return "暂无"
	}

	// 如果 Type 为空，默认使用原始值处理
	formatType := config.Type
	if formatType == "" {
		formatType = FeatureTypeRaw
	}

	switch formatType {
	case FeatureTypeEnum:
		return formatEnumValue(ctx, config, value)
	case FeatureTypeTimestamp:
		return formatTimestampValue(ctx, config, value)
	case FeatureTypePercentage:
		return formatPercentageValue(ctx, config, value)
	case FeatureTypeCurrency:
		return formatCurrencyValue(ctx, config, value)
	case FeatureTypeRaw:
		fallthrough
	default:
		return formatRawValue(value)
	}
}

// formatEnumValue 格式化枚举值
func formatEnumValue(ctx *gin.Context, config LeadsFeatureConfig, value interface{}) string {
	if config.Mapping == nil {
		if ctx != nil {
			zlog.Warnf(ctx, "formatEnumValue: mapping is nil for field %s", config.Name)
		}
		return formatRawValue(value)
	}

	valueStr := formatRawValue(value)
	if mappedValue, exists := config.Mapping[valueStr]; exists {
		return mappedValue
	}

	// 如果找不到映射值，返回原值
	if ctx != nil {
		zlog.Debugf(ctx, "formatEnumValue: no mapping found for field %s, value %s", config.Name, valueStr)
	}
	return valueStr
}

// formatTimestampValue 格式化时间戳值
func formatTimestampValue(ctx *gin.Context, config LeadsFeatureConfig, value interface{}) string {
	// 尝试将值转换为 int64 时间戳
	var timestamp int64
	switch v := value.(type) {
	case int64:
		timestamp = v
	case int:
		timestamp = int64(v)
	case float64:
		timestamp = int64(v)
	case string:
		var err error
		timestamp, err = strconv.ParseInt(v, 10, 64)
		if err != nil {
			if ctx != nil {
				zlog.Warnf(ctx, "formatTimestampValue: failed to parse timestamp for field %s, value %v, err: %v", config.Name, value, err)
			}
			return formatRawValue(value)
		}
	default:
		if ctx != nil {
			zlog.Warnf(ctx, "formatTimestampValue: unsupported type for field %s, value %v", config.Name, value)
		}
		return formatRawValue(value)
	}

	// 检查时间戳是否有效
	if timestamp <= 0 {
		return ""
	}

	// 使用配置的时间格式，如果为空则使用默认格式
	timeFormat := config.TimeFormat
	if timeFormat == "" {
		timeFormat = "2006-01-02 15:04:05"
	}

	return time.Unix(timestamp, 0).Format(timeFormat)
}

// formatPercentageValue 格式化百分比值
func formatPercentageValue(ctx *gin.Context, config LeadsFeatureConfig, value interface{}) string {
	// 尝试将值转换为 float64
	var floatValue float64
	switch v := value.(type) {
	case float64:
		floatValue = v
	case float32:
		floatValue = float64(v)
	case int64:
		floatValue = float64(v)
	case int:
		floatValue = float64(v)
	case string:
		var err error
		floatValue, err = strconv.ParseFloat(v, 64)
		if err != nil {
			if ctx != nil {
				zlog.Warnf(ctx, "formatPercentageValue: failed to parse float for field %s, value %v, err: %v", config.Name, value, err)
			}
			return formatRawValue(value)
		}
	default:
		if ctx != nil {
			zlog.Warnf(ctx, "formatPercentageValue: unsupported type for field %s, value %v", config.Name, value)
		}
		return formatRawValue(value)
	}

	// 使用配置的小数位数，如果为0或负数则使用默认值1
	decimalPlace := config.DecimalPlace
	if decimalPlace <= 0 {
		decimalPlace = 2
	}

	// 转换为百分比并格式化
	percentage := floatValue * 100

	// 格式化为指定小数位数的字符串
	formatted := fmt.Sprintf("%."+strconv.Itoa(decimalPlace)+"f", percentage)

	// 去除末尾无意义的零和小数点
	formatted = removeTrailingZeros(formatted)

	return formatted + "%"
}

// formatCurrencyValue 格式化货币值
func formatCurrencyValue(ctx *gin.Context, config LeadsFeatureConfig, value interface{}) string {
	// 尝试将值转换为 float64
	var floatValue float64
	switch v := value.(type) {
	case float64:
		floatValue = v
	case float32:
		floatValue = float64(v)
	case int64:
		floatValue = float64(v)
	case int:
		floatValue = float64(v)
	case string:
		var err error
		floatValue, err = strconv.ParseFloat(v, 64)
		if err != nil {
			if ctx != nil {
				zlog.Warnf(ctx, "formatCurrencyValue: failed to parse float for field %s, value %v, err: %v", config.Name, value, err)
			}
			return formatRawValue(value)
		}
	default:
		if ctx != nil {
			zlog.Warnf(ctx, "formatCurrencyValue: unsupported type for field %s, value %v", config.Name, value)
		}
		return formatRawValue(value)
	}

	// 使用配置的除数因子，如果为0或负数则使用默认值1
	divisionFactor := config.DivisionFactor
	if divisionFactor <= 0 {
		divisionFactor = 100
	}

	// 使用配置的小数位数
	decimalPlace := config.DecimalPlace

	// 转换货币值（如从分转为元）
	currencyValue := floatValue / float64(divisionFactor)

	// 格式化为指定小数位数的字符串
	formatted := fmt.Sprintf("%."+strconv.Itoa(decimalPlace)+"f", currencyValue)

	// 去除末尾无意义的零和小数点
	formatted = removeTrailingZeros(formatted)

	return formatted
}

// removeTrailingZeros 去除数字字符串末尾的无意义零和小数点
func removeTrailingZeros(s string) string {
	if !strings.Contains(s, ".") {
		return s // 如果没有小数点，直接返回
	}

	// 去除末尾的零
	s = strings.TrimRight(s, "0")

	// 如果小数点后没有数字了，也去除小数点
	s = strings.TrimRight(s, ".")

	return s
}

// formatRawValue 格式化原始值
func formatRawValue(value interface{}) string {
	return fmt.Sprintf("%v", value)
}
