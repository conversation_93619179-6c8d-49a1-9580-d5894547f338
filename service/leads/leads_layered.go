package leads

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/dto/dtoleads"
	"sort"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// 常量
const (
	dateFormatYYYYMMDD    = "20060102"
	dateFormatMMDD        = "01/02"
	dateFormatChineseMMDD = "01月02日"
)

// GetLeadsLayerDetailInfo retrieves layered detail information for a specific course and student.
func GetLeadsLayerDetailInfo(ctx *gin.Context, req dtoleads.GetLeadsLayerDetailInfoReq) (rsp dtoleads.GetLeadsLayerDetailInfoRsp, err error) {
	rsp.PurchaseIntentions = make([]dtoleads.PurchaseIntention, 0)
	rsp.Milestones = make([]dtoleads.Milestone, 0)
	rsp.Features = make([]dtoleads.Feature, 0)

	result, err := dataproxy.GetLeadsAdsBaseModelByCourse(ctx, dataproxy.GetLeadsAdsBaseModelReq{
		CourseId:    req.CourseId,
		StudentUids: cast.ToString(req.StudentUid),
		Fields:      strings.Join([]string{"wx_add_time", "mdc_time", "xzk_time", "inclass_time", "trans_score", "trans_level", "date"}, ","),
	})
	if err != nil || len(result.List) == 0 {
		return
	}

	models := result.List
	sort.Slice(models, func(i, j int) bool {
		return models[i].Date > models[j].Date
	})
	latestModel := models[0]

	rsp.Milestones, _ = buildMilestones(latestModel)
	rsp.PurchaseIntentions, _ = buildPurchaseIntentions(getModelsByAllocTime(models, req.AllocTime))
	rsp.RefreshTime = formatDate(latestModel.Date, dateFormatChineseMMDD)
	rsp.TransLevel = getTransLevelDesc(latestModel.TransLevel)
	rsp.Features, _ = buildFeatures(ctx, req.CourseId, req.StudentUid)

	return rsp, nil
}

// buildFeatures constructs a sorted list of features from the model
// 根据 LeadsFeatureConfig 配置构建特征列表，严格按照配置的顺序和内容展示
func buildFeatures(ctx *gin.Context, courseId, studentUid int64) ([]dtoleads.Feature, error) {
	// 获取需要展示的特征配置
	LeadsFeatureConfig, err := GetLeadsFeatureConfig(ctx)
	if err != nil || len(LeadsFeatureConfig) == 0 {
		zlog.Warnf(ctx, "LeadsFeatureConfig found no config")
		return make([]dtoleads.Feature, 0), nil
	}

	// 以 LeadsFeatureConfig 为基准构建查询参数
	// 提取配置中的所有字段名作为 fields 参数
	configFields := make([]string, 0, len(LeadsFeatureConfig))
	for _, config := range LeadsFeatureConfig {
		configFields = append(configFields, config.Name)
	}
	fieldsParam := strings.Join(configFields, ",")

	// 获取学生特征数据，只请求配置中需要的字段
	featuresList, err := dataproxy.GetLpcObjLeadsFeatureByCourseStudent(ctx, dataproxy.GetLpcObjLeadsFeatureByCourseStudentReq{
		CourseId:    courseId,
		StudentUids: cast.ToString(studentUid),
		Fields:      fieldsParam,
	})
	if err != nil {
		zlog.Errorf(ctx, "GetLpcObjLeadsFeatureByCourseStudent failed, courseId: %d, studentUid: %d, fields: %s, err: %+v",
			courseId, studentUid, fieldsParam, err)
		return nil, err
	}

	var dataMap map[string]any
	if len(featuresList.List) > 0 {
		dataMap = featuresList.List[0]
	} else {
		// 如果没有返回数据，创建空的 map
		dataMap = make(map[string]any)
		zlog.Warnf(ctx, "GetLpcObjLeadsFeatureByCourseStudent returned empty data, courseId: %d, studentUid: %d",
			courseId, studentUid)
	}

	// 按配置顺序构建特征列表，确保返回的特征列表长度与配置字段数量一致
	features := make([]dtoleads.Feature, 0, len(LeadsFeatureConfig))
	for i, config := range LeadsFeatureConfig {
		var valueStr string

		// 检查配置的字段名是否在数据中存在
		if value, exists := dataMap[config.Name]; exists {
			// 使用配置的格式化规则处理字段值
			valueStr = FormatFeatureValue(ctx, config, value)
		} else {
			// 处理缺失字段：在对应位置插入空值
			valueStr = ""
			zlog.Warnf(ctx, "Field %s is missing or nil in dataproxy response, using empty value", config.Name)
		}

		// 无论字段是否存在都要添加到结果中，保持配置的顺序
		feature := dtoleads.Feature{
			Name:  config.Name,
			Desc:  config.Desc,
			Value: valueStr,
			Index: i + 1,
		}
		features = append(features, feature)
	}

	return features, nil
}

// MilestoneConfig defines the mapping for milestone fields.
type MilestoneConfig struct {
	Timestamp int64
	Desc      string
}

// buildMilestones constructs a list of milestones based on model timestamps.
func buildMilestones(model dataproxy.GetLeadsAdsBaseModel) ([]dtoleads.Milestone, error) {
	configs := []MilestoneConfig{
		{model.WxAddTime, "加微时间"},
		{model.MdcTime, "摸底测时间"},
		{model.XzkTime, "1V1出镜时间"},
		{model.InclassTime, "到课时间"},
	}

	milestones := make([]dtoleads.Milestone, 0, len(configs))
	for _, cfg := range configs {
		if cfg.Timestamp > 0 {
			milestones = append(milestones, dtoleads.Milestone{
				Date: formatTimestampToString(cfg.Timestamp, dateFormatMMDD),
				Desc: cfg.Desc,
			})
		}
	}

	return milestones, nil
}

// buildPurchaseIntentions constructs a list of purchase intentions sorted by date.
func buildPurchaseIntentions(models []dataproxy.GetLeadsAdsBaseModel) ([]dtoleads.PurchaseIntention, error) {
	if len(models) == 0 {
		return make([]dtoleads.PurchaseIntention, 0), nil
	}

	// Pre-allocate with estimated capacity
	intentions := make([]dtoleads.PurchaseIntention, 0, len(models))

	// Sort models by date in ascending order
	sortedModels := make([]dataproxy.GetLeadsAdsBaseModel, len(models))
	copy(sortedModels, models)
	sort.Slice(sortedModels, func(i, j int) bool {
		return sortedModels[i].Date < sortedModels[j].Date
	})

	for _, model := range sortedModels {
		intentions = append(intentions, dtoleads.PurchaseIntention{
			Date:  formatDate(model.Date, dateFormatMMDD),
			Score: model.TransScore,
		})
	}

	return intentions, nil
}

// getModelsByAllocTime filters models within the date range around allocTime.
func getModelsByAllocTime(models []dataproxy.GetLeadsAdsBaseModel, allocTime int64) []dataproxy.GetLeadsAdsBaseModel {
	if allocTime <= 0 {
		return nil
	}

	// 这里的时间范围是例子分配时间的当天和后14天
	startDate := time.Unix(allocTime, 0)
	endDate := time.Unix(allocTime, 0).AddDate(0, 0, 14)
	startDateInt := formatTimeToInt(startDate, dateFormatYYYYMMDD)
	endDateInt := formatTimeToInt(endDate, dateFormatYYYYMMDD)

	// Pre-allocate with estimated capacity
	filtered := make([]dataproxy.GetLeadsAdsBaseModel, 0, len(models))
	for _, model := range models {
		if model.Date >= startDateInt && model.Date <= endDateInt {
			filtered = append(filtered, model)
		}
	}

	return filtered
}

// formatTimestampToString formats a Unix timestamp to the specified format.
func formatTimestampToString(timestamp int64, format string) string {
	if timestamp <= 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format(format)
}

// formatTimeToInt formats a time.Time to an integer date (e.g., 20230101).
func formatTimeToInt(t time.Time, format string) int64 {
	dateStr := t.Format(format)
	return cast.ToInt64(dateStr)
}

func getTransLevelDesc(transLevel int) string {
	switch transLevel {
	case 0:
		return "新"
	case 1:
		return "高"
	case 2:
		return "中"
	case 3:
		return "低"
	default:
		return "新"
	}
}

func formatDate(dateInt int64, format string) string {
	dateStr := cast.ToString(dateInt)
	if len(dateStr) != 8 {
		return ""
	}

	t, err := time.Parse(dateFormatYYYYMMDD, dateStr)
	if err != nil {
		return ""
	}
	return t.Format(format)
}
