package leads

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// createTestContext 创建一个用于测试的 gin.Context
func createTestContext() *gin.Context {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = httptest.NewRequest(http.MethodGet, "/test", nil)
	return ctx
}

// TestConvertToInt64 测试 convertToInt64 函数
func TestConvertToInt64(t *testing.T) {
	// 对于错误情况，使用 nil context 避免日志记录问题
	ctx := createTestContext()

	tests := []struct {
		name      string
		fieldName string
		value     interface{}
		expected  int64
		hasError  bool
	}{
		// 正常情况测试
		{
			name:      "int64类型",
			fieldName: "test_field",
			value:     int64(123456789),
			expected:  123456789,
			hasError:  false,
		},
		{
			name:      "int类型",
			fieldName: "test_field",
			value:     int(123),
			expected:  123,
			hasError:  false,
		},
		{
			name:      "float64类型",
			fieldName: "test_field",
			value:     float64(123.456),
			expected:  123,
			hasError:  false,
		},
		{
			name:      "string类型_有效数字",
			fieldName: "test_field",
			value:     "123456789",
			expected:  123456789,
			hasError:  false,
		},
		{
			name:      "string类型_负数",
			fieldName: "test_field",
			value:     "-123",
			expected:  -123,
			hasError:  false,
		},
		{
			name:      "string类型_零",
			fieldName: "test_field",
			value:     "0",
			expected:  0,
			hasError:  false,
		},
		// 边界情况测试
		{
			name:      "最大int64值",
			fieldName: "test_field",
			value:     int64(9223372036854775807),
			expected:  9223372036854775807,
			hasError:  false,
		},
		{
			name:      "最小int64值",
			fieldName: "test_field",
			value:     int64(-9223372036854775808),
			expected:  -9223372036854775808,
			hasError:  false,
		},
		// 错误情况测试
		{
			name:      "string类型_无效数字",
			fieldName: "test_field",
			value:     "invalid_number",
			expected:  0,
			hasError:  true,
		},
		{
			name:      "string类型_空字符串",
			fieldName: "test_field",
			value:     "",
			expected:  0,
			hasError:  true,
		},
		{
			name:      "string类型_浮点数字符串",
			fieldName: "test_field",
			value:     "123.456",
			expected:  0,
			hasError:  true,
		},
		{
			name:      "bool类型_true转换为1",
			fieldName: "test_field",
			value:     true,
			expected:  1,
			hasError:  false,
		},
		{
			name:      "slice类型_不支持",
			fieldName: "test_field",
			value:     []int{1, 2, 3},
			expected:  0,
			hasError:  true,
		},
		{
			name:      "nil值转换为0",
			fieldName: "test_field",
			value:     nil,
			expected:  0,
			hasError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 对于错误情况，使用 nil context 避免日志记录问题
			testCtx := ctx
			if tt.hasError {
				testCtx = nil
			}

			result, err := convertToInt64(testCtx, tt.fieldName, tt.value)

			if tt.hasError {
				assert.Error(t, err, "期望有错误但没有错误")
			} else {
				assert.NoError(t, err, "期望没有错误但有错误: %v", err)
			}

			assert.Equal(t, tt.expected, result, "转换结果不匹配")
		})
	}
}

// TestConvertToInt64_NilContext 测试 convertToInt64 函数在 nil context 下的行为
func TestConvertToInt64_NilContext(t *testing.T) {
	// 测试正常情况
	result, err := convertToInt64(nil, "test_field", int64(123))
	assert.NoError(t, err)
	assert.Equal(t, int64(123), result)

	// 测试错误情况
	result, err = convertToInt64(nil, "test_field", "invalid")
	assert.Error(t, err)
	assert.Equal(t, int64(0), result)
}

// TestConvertToFloat64 测试 convertToFloat64 函数
func TestConvertToFloat64(t *testing.T) {
	ctx := createTestContext()

	tests := []struct {
		name      string
		fieldName string
		value     interface{}
		expected  float64
		hasError  bool
	}{
		// 正常情况测试
		{
			name:      "float64类型",
			fieldName: "test_field",
			value:     float64(123.456),
			expected:  123.456,
			hasError:  false,
		},
		{
			name:      "float32类型",
			fieldName: "test_field",
			value:     float32(123.456),
			expected:  float64(float32(123.456)), // 注意精度转换
			hasError:  false,
		},
		{
			name:      "int64类型",
			fieldName: "test_field",
			value:     int64(123),
			expected:  123.0,
			hasError:  false,
		},
		{
			name:      "int类型",
			fieldName: "test_field",
			value:     int(123),
			expected:  123.0,
			hasError:  false,
		},
		{
			name:      "string类型_有效浮点数",
			fieldName: "test_field",
			value:     "123.456",
			expected:  123.456,
			hasError:  false,
		},
		{
			name:      "string类型_整数",
			fieldName: "test_field",
			value:     "123",
			expected:  123.0,
			hasError:  false,
		},
		{
			name:      "string类型_负数",
			fieldName: "test_field",
			value:     "-123.456",
			expected:  -123.456,
			hasError:  false,
		},
		{
			name:      "string类型_零",
			fieldName: "test_field",
			value:     "0",
			expected:  0.0,
			hasError:  false,
		},
		{
			name:      "string类型_科学计数法",
			fieldName: "test_field",
			value:     "1.23e2",
			expected:  123.0,
			hasError:  false,
		},
		// 边界情况测试
		{
			name:      "极小正数",
			fieldName: "test_field",
			value:     float64(1e-10),
			expected:  1e-10,
			hasError:  false,
		},
		{
			name:      "极大数",
			fieldName: "test_field",
			value:     float64(1e10),
			expected:  1e10,
			hasError:  false,
		},
		// 错误情况测试
		{
			name:      "string类型_无效数字",
			fieldName: "test_field",
			value:     "invalid_number",
			expected:  0,
			hasError:  true,
		},
		{
			name:      "string类型_空字符串",
			fieldName: "test_field",
			value:     "",
			expected:  0,
			hasError:  true,
		},
		{
			name:      "bool类型_true转换为1.0",
			fieldName: "test_field",
			value:     true,
			expected:  1.0,
			hasError:  false,
		},
		{
			name:      "map类型_不支持",
			fieldName: "test_field",
			value:     map[string]int{"key": 1},
			expected:  0,
			hasError:  true,
		},
		{
			name:      "nil值_转换失败",
			fieldName: "test_field",
			value:     nil,
			expected:  0.0,
			hasError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 对于错误情况，使用 nil context 避免日志记录问题
			testCtx := ctx
			if tt.hasError {
				testCtx = nil
			}

			result, err := convertToFloat64(testCtx, tt.fieldName, tt.value)

			if tt.hasError {
				assert.Error(t, err, "期望有错误但没有错误")
			} else {
				assert.NoError(t, err, "期望没有错误但有错误: %v", err)
			}

			assert.Equal(t, tt.expected, result, "转换结果不匹配")
		})
	}
}

// TestConvertToFloat64_NilContext 测试 convertToFloat64 函数在 nil context 下的行为
func TestConvertToFloat64_NilContext(t *testing.T) {
	// 测试正常情况
	result, err := convertToFloat64(nil, "test_field", float64(123.456))
	assert.NoError(t, err)
	assert.Equal(t, 123.456, result)

	// 测试错误情况
	result, err = convertToFloat64(nil, "test_field", "invalid")
	assert.Error(t, err)
	assert.Equal(t, float64(0), result)
}

// TestFormatTimestampValue_Integration 测试重构后的 formatTimestampValue 函数
func TestFormatTimestampValue_Integration(t *testing.T) {
	ctx := createTestContext()

	tests := []struct {
		name     string
		config   LeadsFeatureConfig
		value    interface{}
		expected string
	}{
		{
			name: "int64时间戳_默认格式",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    int64(1609459200),     // 2021-01-01 00:00:00 UTC
			expected: "2021-01-01 08:00:00", // 转换为本地时间
		},
		{
			name: "string时间戳_自定义格式",
			config: LeadsFeatureConfig{
				Name:       "timestamp_field",
				TimeFormat: "2006-01-02",
			},
			value:    "1609459200",
			expected: "2021-01-01",
		},
		{
			name: "int类型时间戳",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    int(1609459200),
			expected: "2021-01-01 08:00:00",
		},
		{
			name: "float64类型时间戳",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    float64(1609459200),
			expected: "2021-01-01 08:00:00",
		},
		{
			name: "无效时间戳_返回空",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    int64(0),
			expected: "",
		},
		{
			name: "负数时间戳_返回空",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    int64(-1),
			expected: "",
		},
		{
			name: "无效字符串_返回原值",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    "invalid_timestamp",
			expected: "invalid_timestamp",
		},
		{
			name: "不支持的类型_返回原值",
			config: LeadsFeatureConfig{
				Name: "timestamp_field",
			},
			value:    []int{1, 2, 3},
			expected: "[1 2 3]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 对于错误情况，使用 nil context 避免日志记录问题
			testCtx := ctx
			if tt.expected == tt.value || tt.expected == "[1 2 3]" {
				testCtx = nil
			}

			result := formatTimestampValue(testCtx, tt.config, tt.value)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestFormatPercentageValue_Integration 测试重构后的 formatPercentageValue 函数
func TestFormatPercentageValue_Integration(t *testing.T) {
	ctx := createTestContext()

	tests := []struct {
		name     string
		config   LeadsFeatureConfig
		value    interface{}
		expected string
	}{
		{
			name: "float64百分比_默认精度",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    float64(0.85),
			expected: "85%",
		},
		{
			name: "float64百分比_自定义精度",
			config: LeadsFeatureConfig{
				Name:         "percentage_field",
				DecimalPlace: 1,
			},
			value:    float64(0.8567),
			expected: "85.7%",
		},
		{
			name: "int类型百分比",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    int(1),
			expected: "100%",
		},
		{
			name: "string类型百分比",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    "0.5",
			expected: "50%",
		},
		{
			name: "float32类型百分比",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    float32(0.25),
			expected: "25%",
		},
		{
			name: "零值百分比",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    float64(0),
			expected: "0%",
		},
		{
			name: "无效字符串_返回原值",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    "invalid_number",
			expected: "invalid_number",
		},
		{
			name: "不支持的类型_返回原值",
			config: LeadsFeatureConfig{
				Name: "percentage_field",
			},
			value:    map[string]int{"key": 1},
			expected: "map[key:1]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 对于错误情况，使用 nil context 避免日志记录问题
			testCtx := ctx
			if tt.expected == tt.value || tt.expected == "map[key:1]" {
				testCtx = nil
			}

			result := formatPercentageValue(testCtx, tt.config, tt.value)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestFormatCurrencyValue_Integration 测试重构后的 formatCurrencyValue 函数
func TestFormatCurrencyValue_Integration(t *testing.T) {
	ctx := createTestContext()

	tests := []struct {
		name     string
		config   LeadsFeatureConfig
		value    interface{}
		expected string
	}{
		{
			name: "分转元_默认配置",
			config: LeadsFeatureConfig{
				Name: "currency_field",
			},
			value:    int64(12345), // 123.45元
			expected: "123.45",
		},
		{
			name: "自定义除数因子",
			config: LeadsFeatureConfig{
				Name:           "currency_field",
				DivisionFactor: 1000,
				DecimalPlace:   3,
			},
			value:    int64(12345),
			expected: "12.345",
		},
		{
			name: "float64货币值",
			config: LeadsFeatureConfig{
				Name: "currency_field",
			},
			value:    float64(12345.67),
			expected: "123.46", // 四舍五入
		},
		{
			name: "string类型货币值",
			config: LeadsFeatureConfig{
				Name: "currency_field",
			},
			value:    "10000",
			expected: "100",
		},
		{
			name: "零值货币",
			config: LeadsFeatureConfig{
				Name: "currency_field",
			},
			value:    int64(0),
			expected: "0",
		},
		{
			name: "无效字符串_返回原值",
			config: LeadsFeatureConfig{
				Name: "currency_field",
			},
			value:    "invalid_amount",
			expected: "invalid_amount",
		},
		{
			name: "不支持的类型_返回原值",
			config: LeadsFeatureConfig{
				Name: "currency_field",
			},
			value:    []string{"invalid"},
			expected: "[invalid]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 对于错误情况，使用 nil context 避免日志记录问题
			testCtx := ctx
			if tt.expected == tt.value || tt.expected == "[invalid]" {
				testCtx = nil
			}

			result := formatCurrencyValue(testCtx, tt.config, tt.value)
			assert.Equal(t, tt.expected, result)
		})
	}
}
